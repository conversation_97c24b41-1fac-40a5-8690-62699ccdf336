# 数据抽样配置

# 源数据和目标数据目录
INPUT_DIR: "data/processed"
OUTPUT_DIR: "data/sampled"

# 随机种子，用于确保抽样结果的可重现性
RANDOM_SEED: 42

# 数据集抽样配置
# 格式: 数据集路径: {sample_size: 抽样量, exclude_files: [不抽取的文件列表]}
# 或者简化格式: 数据集路径: 抽样量 (表示抽取所有文件)
SAMPLING_CONFIG:
  # ConvFinQA数据集
  "fin-r1/fingpt-convfinqa":
    sample_size: 8000
    exclude_files: []

  # Finance-Instruct数据集
  "fin-r1/Finance-Instruct-500k":
    sample_size: 12000
    exclude_files: []

  # FinCUGE数据集
  "fin-r1/FinCUGE-Instruction":
    sample_size: 2000
    exclude_files: []

  # FinQA数据集
  "fin-r1/finqa":
    sample_size: 3000
    exclude_files: []

  # TFNS数据集
  "fin-r1/twitter-financial-news-sentiment":
    sample_size: 2500
    exclude_files: []

  # FinanceIQ数据集
  "fin-r1/FinanceIQ":
    sample_size: 2600
    exclude_files: []

  # FinanceQT数据集 (全量抽样)
  "fin-r1/Quant-Trading-Instruct":
    sample_size: 386
    exclude_files: []

  # Ant_Finance数据集
  "fin-r1/ant_finance":
    sample_size: 800
    exclude_files: []

  # Ant_Finance_SUFE数据集 (排除test文件，因为没有答案)
  "fin-r1/ant_finance_SUFE":
    sample_size: 800
    exclude_files: ["sufe_test_3340.jsonl"]

  # FinCorpus数据集
  "fin-r1/FinCorpus":
    sample_size: 30000
    exclude_files: []

# 抽样策略配置
SAMPLING_STRATEGY:
  # 抽样方法: "random" (随机抽样) 或 "stratified" (分层抽样)
  method: "random"

  # 是否保持原始文件结构 (如果数据集有多个文件，是否按比例从每个文件抽样)
  preserve_file_structure: false

  # 如果抽样量大于数据集总量，是否使用全部数据
  use_all_if_insufficient: true

# 日志配置
LOGGING:
  # 是否启用详细日志
  verbose: true

  # 是否显示抽样统计信息
  show_statistics: true
