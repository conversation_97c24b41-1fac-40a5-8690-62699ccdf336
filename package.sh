#!/bin/bash

# 打包脚本 - 使用git归档功能，确保单个zip不超过100KB

# 设置变量
PROJECT_NAME="fin_data_synthesis"
OUTPUT_DIR="./dist"
TEMP_DIR="./temp"
MAX_SIZE=100 # 最大大小（KB）
SPLIT_SIZE=100 # 拆分大小（KB）

# 创建必要的目录
mkdir -p "$OUTPUT_DIR"
mkdir -p "$TEMP_DIR"

# 清理旧的打包文件
rm -rf "$OUTPUT_DIR"/*
rm -rf "$TEMP_DIR"/*

echo "正在使用git归档项目..."

# 使用git归档功能创建一个完整的归档文件
# 这会自动排除.gitignore中指定的文件
git archive --format=zip -o "$TEMP_DIR/${PROJECT_NAME}_full.zip" HEAD

# 获取归档文件大小（KB）
ARCHIVE_SIZE=$(du -k "$TEMP_DIR/${PROJECT_NAME}_full.zip" | cut -f1)
echo "归档文件大小: ${ARCHIVE_SIZE}KB"

# 判断是否需要拆分
if [ "$ARCHIVE_SIZE" -le "$MAX_SIZE" ]; then
    # 如果文件小于限制大小，直接移动到输出目录
    echo "归档文件小于${MAX_SIZE}KB，无需拆分"
    mv "$TEMP_DIR/${PROJECT_NAME}_full.zip" "$OUTPUT_DIR/${PROJECT_NAME}.zip"
else
    # 如果文件大于限制大小，使用split命令拆分
    echo "归档文件大于${MAX_SIZE}KB，需要拆分"

    # 拆分为多个部分，每个部分不超过SPLIT_SIZE KB
    # 使用zip -s选项拆分
    echo "正在拆分文件..."

    # 计算拆分大小（字节）
    SPLIT_BYTES=$((SPLIT_SIZE * 1024))

    # 拆分zip文件
    cd "$TEMP_DIR"
    zip -s $SPLIT_BYTES --out "../$OUTPUT_DIR/${PROJECT_NAME}_split.zip" "${PROJECT_NAME}_full.zip"
    cd ..

    # 删除原始大文件
    rm "$TEMP_DIR/${PROJECT_NAME}_full.zip"
fi

# 显示结果
echo "打包完成！"
echo "文件保存在 $OUTPUT_DIR 目录中"

# 列出创建的zip文件及其大小
echo "创建的文件列表:"
ls -lh "$OUTPUT_DIR"

# 清理临时文件
rm -rf "$TEMP_DIR"
