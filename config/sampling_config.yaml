# 数据抽样配置

# 源数据和目标数据目录
INPUT_DIR: "data/processed"
OUTPUT_DIR: "data/sampled"

# 随机种子，用于确保抽样结果的可重现性
RANDOM_SEED: 42

# 数据集抽样配置
# 格式: 数据集路径: 抽样量
SAMPLING_CONFIG:
  # ConvFinQA数据集
  "fin-r1/fingpt-convfinqa": 8000
  
  # Finance-Instruct数据集
  "fin-r1/Finance-Instruct-500k": 12000
  
  # FinCUGE数据集
  "fin-r1/FinCUGE-Instruction": 2000
  
  # FinQA数据集
  "fin-r1/finqa": 3000
  
  # TFNS数据集
  "fin-r1/twitter-financial-news-sentiment": 2500
  
  # FinanceIQ数据集
  "fin-r1/FinanceIQ": 2600
  
  # FinanceQT数据集 (全量抽样)
  "fin-r1/Quant-Trading-Instruct": 386
  
  # Ant_Finance数据集
  "fin-r1/ant_finance": 800
  
  # Ant_Finance_SUFE数据集
  "fin-r1/ant_finance_SUFE": 800
  
  # FinCorpus数据集
  "fin-r1/FinCorpus": 30000

# 抽样策略配置
SAMPLING_STRATEGY:
  # 抽样方法: "random" (随机抽样) 或 "stratified" (分层抽样)
  method: "random"
  
  # 是否保持原始文件结构 (如果数据集有多个文件，是否按比例从每个文件抽样)
  preserve_file_structure: true
  
  # 如果抽样量大于数据集总量，是否使用全部数据
  use_all_if_insufficient: true

# 日志配置
LOGGING:
  # 是否启用详细日志
  verbose: true
  
  # 是否显示抽样统计信息
  show_statistics: true
