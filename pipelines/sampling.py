#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据抽样脚本

该脚本用于对data/processed目录下的数据集进行随机抽样。
主要功能：
- 加载data/processed目录下的所有数据集
- 根据配置文件中的抽样量对每个数据集进行随机抽样
- 收集每个数据集下的所有数据文件到一个list后再进行抽样
- 将抽样结果保存到data/sampled目录下，保持相同的目录结构

使用方法：
    python pipelines/sampling.py [--config CONFIG_PATH]
"""

import os
import json
import glob
import yaml
import random
import argparse
from pathlib import Path
from typing import Dict, Any, List
from collections import defaultdict


def load_config(config_path: str = "config/sampling_config.yaml") -> Dict[str, Any]:
    """
    加载配置文件

    Args:
        config_path: 配置文件路径

    Returns:
        Dict[str, Any]: 配置信息
    """
    try:
        # 确保配置文件路径是绝对路径
        if not os.path.isabs(config_path):
            config_path = os.path.abspath(config_path)

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        print(f"尝试加载的路径: {config_path}")
        return {}


def load_jsonl_file(file_path: str) -> List[Dict[str, Any]]:
    """
    加载JSONL文件

    Args:
        file_path: JSONL文件路径

    Returns:
        List[Dict[str, Any]]: 数据列表
    """
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    data.append(json.loads(line))
    except Exception as e:
        print(f"加载文件失败 {file_path}: {str(e)}")
    return data


def save_jsonl_file(data: List[Dict[str, Any]], file_path: str) -> None:
    """
    保存数据到JSONL文件

    Args:
        data: 要保存的数据列表
        file_path: 输出文件路径
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
    except Exception as e:
        print(f"保存文件失败 {file_path}: {str(e)}")


def collect_dataset_files(input_dir: str, dataset_path: str, exclude_files: List[str] = None) -> List[str]:
    """
    收集数据集下的所有JSONL文件

    Args:
        input_dir: 输入根目录
        dataset_path: 数据集相对路径
        exclude_files: 要排除的文件名列表

    Returns:
        List[str]: 文件路径列表
    """
    if exclude_files is None:
        exclude_files = []

    full_dataset_path = os.path.join(input_dir, dataset_path)

    # 查找所有JSONL文件
    jsonl_files = []
    if os.path.isdir(full_dataset_path):
        # 如果是目录，递归查找所有JSONL文件
        jsonl_files = glob.glob(os.path.join(full_dataset_path, "**/*.jsonl"), recursive=True)
    else:
        # 如果指定的是文件，直接添加
        if full_dataset_path.endswith('.jsonl') and os.path.exists(full_dataset_path):
            jsonl_files = [full_dataset_path]

    # 过滤掉要排除的文件
    if exclude_files:
        filtered_files = []
        for file_path in jsonl_files:
            file_name = os.path.basename(file_path)
            if file_name not in exclude_files:
                filtered_files.append(file_path)
            else:
                print(f"  排除文件: {os.path.relpath(file_path, input_dir)}")
        jsonl_files = filtered_files

    return sorted(jsonl_files)


def sample_dataset(dataset_files: List[str], sample_size: int,
                  preserve_structure: bool = True,
                  use_all_if_insufficient: bool = True,
                  random_seed: int = 42) -> Dict[str, List[Dict[str, Any]]]:
    """
    对数据集进行抽样

    Args:
        dataset_files: 数据集文件列表
        sample_size: 抽样数量
        preserve_structure: 是否保持文件结构
        use_all_if_insufficient: 如果数据不足是否使用全部数据
        random_seed: 随机种子

    Returns:
        Dict[str, List[Dict[str, Any]]]: 文件路径到抽样数据的映射
    """
    # 设置随机种子
    random.seed(random_seed)

    # 收集所有数据
    all_data = []
    file_data_map = {}

    for file_path in dataset_files:
        data = load_jsonl_file(file_path)
        file_data_map[file_path] = data
        all_data.extend([(item, file_path) for item in data])

    total_size = len(all_data)

    if total_size == 0:
        print(f"警告: 数据集为空")
        return {}

    # 确定实际抽样数量
    actual_sample_size = min(sample_size, total_size)
    if sample_size > total_size:
        if use_all_if_insufficient:
            print(f"警告: 请求抽样 {sample_size} 条，但只有 {total_size} 条数据，将使用全部数据")
            actual_sample_size = total_size
        else:
            print(f"错误: 请求抽样 {sample_size} 条，但只有 {total_size} 条数据")
            return {}

    if preserve_structure and len(dataset_files) > 1:
        # 保持文件结构：按比例从每个文件抽样
        result = {}

        for file_path in dataset_files:
            file_data = file_data_map[file_path]
            file_size = len(file_data)

            if file_size == 0:
                result[file_path] = []
                continue

            # 计算该文件应该抽样的数量
            file_sample_size = int(actual_sample_size * file_size / total_size)

            # 确保至少抽样1条（如果文件不为空且总抽样数大于0）
            if file_sample_size == 0 and actual_sample_size > 0 and file_size > 0:
                file_sample_size = 1

            # 从该文件中随机抽样
            if file_sample_size >= file_size:
                sampled_data = file_data.copy()
            else:
                sampled_data = random.sample(file_data, file_sample_size)

            result[file_path] = sampled_data

        # 检查总抽样数量，如果不足则从最大的文件补充
        total_sampled = sum(len(data) for data in result.values())
        if total_sampled < actual_sample_size:
            # 找到最大的文件
            largest_file = max(dataset_files, key=lambda f: len(file_data_map[f]))
            shortage = actual_sample_size - total_sampled

            # 从最大文件的剩余数据中补充
            largest_file_data = file_data_map[largest_file]
            already_sampled = result[largest_file]
            remaining_data = [item for item in largest_file_data if item not in already_sampled]

            if remaining_data:
                additional_samples = random.sample(remaining_data, min(shortage, len(remaining_data)))
                result[largest_file].extend(additional_samples)

        return result
    else:
        # 不保持文件结构：从所有数据中随机抽样
        if actual_sample_size >= total_size:
            sampled_items = all_data.copy()
        else:
            sampled_items = random.sample(all_data, actual_sample_size)

        # 按文件分组
        result = defaultdict(list)
        for item, file_path in sampled_items:
            result[file_path].append(item)

        return dict(result)


def process_dataset(input_dir: str, output_dir: str, dataset_path: str,
                   sample_size: int, exclude_files: List[str], config: Dict[str, Any]) -> None:
    """
    处理单个数据集

    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        dataset_path: 数据集路径
        sample_size: 抽样数量
        exclude_files: 要排除的文件列表
        config: 配置信息
    """
    print(f"\n处理数据集: {dataset_path}")

    # 收集数据集文件
    dataset_files = collect_dataset_files(input_dir, dataset_path, exclude_files)

    if not dataset_files:
        print(f"警告: 未找到数据集文件 {dataset_path}")
        return

    print(f"找到 {len(dataset_files)} 个文件:")
    for file_path in dataset_files:
        file_size = len(load_jsonl_file(file_path))
        print(f"  - {os.path.relpath(file_path, input_dir)}: {file_size} 条数据")

    # 获取抽样策略配置
    sampling_strategy = config.get("SAMPLING_STRATEGY", {})
    preserve_structure = sampling_strategy.get("preserve_file_structure", True)
    use_all_if_insufficient = sampling_strategy.get("use_all_if_insufficient", True)
    random_seed = config.get("RANDOM_SEED", 42)

    # 执行抽样
    sampled_data = sample_dataset(
        dataset_files,
        sample_size,
        preserve_structure,
        use_all_if_insufficient,
        random_seed
    )

    if not sampled_data:
        print(f"抽样失败: {dataset_path}")
        return

    # 保存抽样结果
    total_sampled = 0
    for file_path, data in sampled_data.items():
        if not data:
            continue

        # 构建输出路径
        rel_path = os.path.relpath(file_path, input_dir)
        output_path = os.path.join(output_dir, rel_path)

        # 保存数据
        save_jsonl_file(data, output_path)
        total_sampled += len(data)

        print(f"  保存 {len(data)} 条数据到: {output_path}")

    print(f"数据集 {dataset_path} 抽样完成: {total_sampled} 条数据")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据抽样脚本")
    parser.add_argument("--config", type=str, default="config/sampling_config.yaml",
                        help="配置文件路径 (默认: config/sampling_config.yaml)")
    args = parser.parse_args()

    # 确保配置文件路径是绝对路径
    if not os.path.isabs(args.config):
        args.config = os.path.abspath(args.config)

    return args


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 加载配置
    config = load_config(args.config)
    if not config:
        print(f"无法加载配置文件: {args.config}")
        return

    # 获取配置参数
    input_dir = config.get("INPUT_DIR", "data/processed")
    output_dir = config.get("OUTPUT_DIR", "data/sampled")
    sampling_config = config.get("SAMPLING_CONFIG", {})
    logging_config = config.get("LOGGING", {})

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    if not sampling_config:
        print("错误: 配置文件中未找到SAMPLING_CONFIG")
        return

    print(f"数据抽样开始")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print(f"随机种子: {config.get('RANDOM_SEED', 42)}")

    # 统计信息
    total_datasets = len(sampling_config)
    processed_datasets = 0
    total_sampled = 0

    # 处理每个数据集
    for dataset_path, dataset_config in sampling_config.items():
        try:
            # 解析数据集配置，支持两种格式：
            # 1. 简化格式: dataset_path: sample_size
            # 2. 完整格式: dataset_path: {sample_size: xxx, exclude_files: []}
            if isinstance(dataset_config, int):
                # 简化格式
                sample_size = dataset_config
                exclude_files = []
            elif isinstance(dataset_config, dict):
                # 完整格式
                sample_size = dataset_config.get("sample_size", 0)
                exclude_files = dataset_config.get("exclude_files", [])
            else:
                print(f"错误: 数据集 {dataset_path} 的配置格式不正确")
                continue

            if sample_size <= 0:
                print(f"警告: 数据集 {dataset_path} 的抽样数量无效: {sample_size}")
                continue

            process_dataset(input_dir, output_dir, dataset_path, sample_size, exclude_files, config)
            processed_datasets += 1
        except Exception as e:
            print(f"处理数据集 {dataset_path} 时发生错误: {str(e)}")

    # 显示统计信息
    if logging_config.get("show_statistics", True):
        print(f"\n=== 抽样统计 ===")
        print(f"总数据集数: {total_datasets}")
        print(f"成功处理: {processed_datasets}")
        print(f"失败数量: {total_datasets - processed_datasets}")

    print(f"\n数据抽样完成")


if __name__ == "__main__":
    main()
